<?php

namespace Tests\Feature;

use App\Models\Assessment;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentResult;
use App\Models\SchoolClass;
use App\Models\Subject;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MultiAttemptAssessmentTest extends TestCase
{
    use RefreshDatabase;

    protected $student;
    protected $assessment;
    protected $questions;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data manually
        $class = SchoolClass::create([
            'name' => 'Test Class',
            'description' => 'Test class for multi-attempt testing'
        ]);

        $subject = Subject::create([
            'name' => 'Test Subject',
            'description' => 'Test subject',
            'class_id' => $class->id
        ]);

        $this->student = User::create([
            'name' => 'Test Student',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'student',
            'class_id' => $class->id
        ]);

        $this->assessment = Assessment::create([
            'title' => 'Test Assessment',
            'description' => 'Test assessment for multi-attempt testing',
            'class_id' => $class->id,
            'subject_id' => $subject->id
        ]);

        // Create test questions
        for ($i = 1; $i <= 5; $i++) {
            AssessmentQuestion::create([
                'assessment_id' => $this->assessment->id,
                'question' => "Test question {$i}",
                'question_type' => 'text',
                'option_a' => 'Option A',
                'option_b' => 'Option B',
                'option_c' => 'Option C',
                'option_d' => 'Option D',
                'correct_answer' => 'A'
            ]);
        }

        $this->questions = AssessmentQuestion::where('assessment_id', $this->assessment->id)->get();
    }

    public function test_student_can_start_first_attempt()
    {
        $response = $this->getJson("/api/assessments/details/{$this->assessment->id}/{$this->student->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'attempt_info' => [
                    'current_attempt' => 1,
                    'completed_attempts' => 0,
                    'is_retake' => false
                ]
            ]);
    }

    public function test_student_can_complete_multiple_attempts()
    {
        // First attempt - score 60%
        $this->completeAttempt(['A', 'A', 'A', 'B', 'B']); // 3/5 = 60%

        // Second attempt - score 80%
        $this->completeAttempt(['A', 'A', 'A', 'A', 'B']); // 4/5 = 80%

        // Third attempt - score 40%
        $this->completeAttempt(['A', 'A', 'B', 'B', 'B']); // 2/5 = 40%

        // Check that we have 3 attempts
        $attempts = AssessmentResult::where('assessment_id', $this->assessment->id)
            ->where('student_id', $this->student->id)
            ->where('progress', 'completed')
            ->get();

        $this->assertCount(3, $attempts);

        // Check average score calculation
        $averageScore = AssessmentResult::getAverageScore($this->assessment->id, $this->student->id);
        $this->assertEquals(60, $averageScore); // (60 + 80 + 40) / 3 = 60
    }

    public function test_student_can_start_retake_after_completion()
    {
        // Complete first attempt
        $this->completeAttempt(['A', 'A', 'A', 'B', 'B']);

        // Start second attempt
        $response = $this->getJson("/api/assessments/details/{$this->assessment->id}/{$this->student->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'attempt_info' => [
                    'current_attempt' => 2,
                    'completed_attempts' => 1,
                    'is_retake' => true
                ]
            ]);
    }

    public function test_can_retake_endpoint_works()
    {
        $response = $this->getJson("/api/assessments/{$this->assessment->id}/can-retake/{$this->student->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'can_retake' => true,
                'current_attempts' => 0,
                'next_attempt_number' => 1
            ]);
    }

    public function test_attempt_history_endpoint_works()
    {
        // Complete two attempts
        $this->completeAttempt(['A', 'A', 'A', 'B', 'B']); // 60%
        $this->completeAttempt(['A', 'A', 'A', 'A', 'B']); // 80%

        $response = $this->getJson("/api/assessments/{$this->assessment->id}/attempts/{$this->student->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'average_score' => 70.0,
                    'total_attempts' => 2
                ]
            ]);
    }

    private function completeAttempt(array $answers)
    {
        // Start assessment
        $startResponse = $this->getJson("/api/assessments/details/{$this->assessment->id}/{$this->student->id}");
        $resultId = $startResponse->json('result_id');

        // Submit answers
        $submissionData = [
            'result_id' => $resultId,
            'answers' => [],
            'user_id' => $this->student->id
        ];

        foreach ($this->questions as $index => $question) {
            $submissionData['answers'][] = [
                'question_id' => $question->id,
                'answer' => $answers[$index]
            ];
        }

        $this->postJson("/api/assessments/{$this->assessment->id}/submit", $submissionData);
    }
}
