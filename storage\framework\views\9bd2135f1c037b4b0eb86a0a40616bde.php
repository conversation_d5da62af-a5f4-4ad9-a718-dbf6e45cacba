<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>
        <?php echo e($class_name->name); ?> - QR Codes
    </title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 32px;
            margin-bottom: 10px;
        }

        .header p {
            color: #555;
            font-size: 18px;
            margin: 0;
        }

        .qr-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .qr-card {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .student-name {
            color: #333;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .qr-code-container {
            margin: 15px 0;
        }

        .qr-code-container img {
            max-width: 150px;
            height: auto;
            border: 2px solid #ffd93d;
            border-radius: 10px;
        }

        .student-info {
            color: #555;
            font-size: 14px;
            margin-top: 10px;
        }
    </style>




</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Student QR Codes - <?php echo e($class_name->name); ?></h1>
            <p>Print these QR codes for students to use for quick login</p>
        </div>

        <div class="qr-grid">
            <?php $__currentLoopData = $students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="qr-card">
                    <div class="student-name"><?php echo e($student->name); ?></div>
                    <div class="qr-code-container">
                        <?php echo $student->qrcode; ?>

                    </div>
                    <div class="student-info">Student Pin: <?php echo e($student->pin); ?><br>Scan to login</div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        </div>
    </div>

</body>

</html>
<?php /**PATH E:\websites\nthanda\resources\views/qrcode.blade.php ENDPATH**/ ?>