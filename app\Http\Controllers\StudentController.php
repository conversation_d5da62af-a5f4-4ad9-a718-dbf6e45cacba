<?php

namespace App\Http\Controllers;

use App\Models\SchoolClass;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use PDF;

class StudentController extends BaseController
{
    public function index(Request $request)
    {
        $this->class_id = 1;
        if ($request->has('class_id')) {

            $this->class_id = $request->class_id;
        }

        $classes = SchoolClass::all();
        $class = SchoolClass::find($this->class_id);
        $students = User::where('class_id', $this->class_id)->paginate(10);
        $this->data = [
            'students' => $students,
            'class_id' => $this->class_id,
            'className' => $class->name,
            'classes' => $classes,
        ];
        return Inertia::render('students/Index', $this->data);
    }

    /**
     * Store a newly created student in storage with auto-generated PIN.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'class_id' => 'required|exists:school_classes,id',
        ]);

        // Generate a unique 4-digit PIN
        $pin = null;
        do {
            $pin = rand(1000, 9999);
            $exists = User::where('pin', $pin)->exists();
            $qrcode = QrCode::size(300)->generate($pin);
        } while ($exists);

        // Generate a unique QR code token
        $qrCodeToken = null;
        do {
            $qrCodeToken = 'STU_' . Str::random(32);
            $tokenExists = User::where('qr_code_token', $qrCodeToken)->exists();
        } while ($tokenExists);

        $student = User::create([
            'name' => $request->name,
            'role' => 'student',
            'class_id' => $request->class_id,
            'pin' => $pin,
            'qr_code_token' => $qrCodeToken,
            'qrcode' => $qrcode,
        ]);

        return redirect()->back()->with('success', "Student created successfully with PIN: {$pin}");
    }

    public function export($class_id)
    {
        $students = User::where('class_id', $class_id)->get();
        $class = SchoolClass::find($class_id);
        $data = [
            'students' => $students,
            'class_name' => $class,
        ];

        $pdf = PDF::loadView('qrcode', $data);

        return $pdf->download('qrcode-' . $class->name . '.pdf', ['Content-Type' => 'application/pdf']);
    }

    /**
     * Update the specified student in storage.
     */
    public function update(Request $request, $id)
    {
        $student = User::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $student->update([
            'name' => $request->name,
        ]);

        return redirect()->back()->with('success', 'Student updated successfully');
    }

    /**
     * Remove the specified student from storage.
     */
    public function destroy($id)
    {
        $student = User::findOrFail($id);
        $student->delete();

        return redirect()->back()->with('success', 'Student deleted successfully');
    }

    /**
     * Generate QR code tokens for students who don't have them.
     */
    public function generateQRTokens()
    {
        $studentsWithoutQR = User::where('role', 'student')
            ->whereNull('qr_code_token')
            ->get();

        $count = 0;
        foreach ($studentsWithoutQR as $student) {
            // Generate a unique QR code token
            $qrCodeToken = null;
            do {
                $qrCodeToken = 'STU_' . Str::random(32);
                $tokenExists = User::where('qr_code_token', $qrCodeToken)->exists();
            } while ($tokenExists);

            $student->update(['qr_code_token' => $qrCodeToken]);
            $count++;
        }

        return redirect()->back()->with('success', "Generated QR tokens for {$count} students");
    }

    /**
     * Get QR code for a specific student.
     */
    public function getStudentQR($id)
    {
        $student = User::findOrFail($id);

        if (!$student->qr_code_token) {
            return response()->json(['error' => 'Student does not have a QR code token'], 404);
        }

        return response()->json([
            'student' => [
                'id' => $student->id,
                'name' => $student->name,
                'qr_token' => $student->qr_code_token
            ]
        ]);
    }
}
