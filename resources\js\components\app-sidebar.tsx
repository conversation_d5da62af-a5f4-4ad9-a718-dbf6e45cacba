import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from '@/components/ui/sidebar';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { BookA, ChevronRight, LayoutGrid, UserCog, Users } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
        route: route('dashboard'),
    },
    {
        title: 'Students',
        href: '/students',
        icon: Users,
        route: route('students.index'),
    },
    {
        title: 'Subjects',
        href: '/subjects',
        icon: BookA,
        route: route('subjects.index'),
    },
    {
        title: 'User Management',
        href: '/users',
        icon: UserCog,
        route: route('users.index'),
    },
];

interface SchoolClass {
    id: number;
    name: string;
}

// Function to handle navigation to units, topics, and lessons

export function AppSidebar() {
    const { auth } = usePage<SharedData>().props;
    const schoolClasses = usePage().props.schoolClasses as SchoolClass[];
    const url = usePage().url;
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <SidebarContent className="gap-0">
                    {/* We create a collapsible SidebarGroup for each parent. */}

                    {auth.user?.role === 'admin' && (
                        <Collapsible defaultOpen={url.includes('units')} title="Units" className="group/collapsible">
                            <SidebarGroup>
                                <SidebarGroupLabel
                                    asChild
                                    className="group/label text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground cursor-pointer text-sm"
                                >
                                    <CollapsibleTrigger>
                                        Units
                                        <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
                                    </CollapsibleTrigger>
                                </SidebarGroupLabel>
                                <CollapsibleContent>
                                    <SidebarGroupContent>
                                        <SidebarMenu>
                                            {schoolClasses.map((className: SchoolClass) => (
                                                <SidebarMenuItem key={className.id}>
                                                    <SidebarMenuButton
                                                        asChild
                                                        isActive={url.includes(`units/${className.id}`)}
                                                        className="cursor-pointer"
                                                    >
                                                        <Link href={route('units.index', { class_id: className.id })}>{className.name}</Link>
                                                    </SidebarMenuButton>
                                                </SidebarMenuItem>
                                            ))}
                                        </SidebarMenu>
                                    </SidebarGroupContent>
                                </CollapsibleContent>
                            </SidebarGroup>
                        </Collapsible>
                    )}
                    {auth.user?.role === 'admin' && (
                        <Collapsible defaultOpen={url.includes('topics')} title="Topics" className="group/collapsible">
                            <SidebarGroup>
                                <SidebarGroupLabel
                                    asChild
                                    className="group/label text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground cursor-pointer text-sm"
                                >
                                    <CollapsibleTrigger>
                                        Topics
                                        <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
                                    </CollapsibleTrigger>
                                </SidebarGroupLabel>
                                <CollapsibleContent>
                                    <SidebarGroupContent>
                                        <SidebarMenu>
                                            {schoolClasses.map((className: SchoolClass) => (
                                                <SidebarMenuItem key={className.id}>
                                                    <SidebarMenuButton
                                                        asChild
                                                        isActive={url.includes(`topics/${className.id}`)}
                                                        className="cursor-pointer"
                                                    >
                                                        <Link href={route('topics.index', { class_id: className.id })}>{className.name}</Link>
                                                    </SidebarMenuButton>
                                                </SidebarMenuItem>
                                            ))}
                                        </SidebarMenu>
                                    </SidebarGroupContent>
                                </CollapsibleContent>
                            </SidebarGroup>
                        </Collapsible>
                    )}
                    <Collapsible defaultOpen={url.includes('assessments')} title="Assessments" className="group/collapsible">
                        <SidebarGroup>
                            <SidebarGroupLabel
                                asChild
                                className="group/label text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground cursor-pointer text-sm"
                            >
                                <CollapsibleTrigger>
                                    Assessments
                                    <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
                                </CollapsibleTrigger>
                            </SidebarGroupLabel>
                            <CollapsibleContent>
                                <SidebarGroupContent>
                                    <SidebarMenu>
                                        {schoolClasses.map((className: SchoolClass) => (
                                            <SidebarMenuItem key={className.id}>
                                                <SidebarMenuButton
                                                    asChild
                                                    isActive={url.includes(`assessments/${className.id}`)}
                                                    className="cursor-pointer"
                                                >
                                                    <Link href={route('assessments.index', { class_id: className.id })}>{className.name}</Link>
                                                </SidebarMenuButton>
                                            </SidebarMenuItem>
                                        ))}
                                    </SidebarMenu>
                                </SidebarGroupContent>
                            </CollapsibleContent>
                        </SidebarGroup>
                    </Collapsible>
                </SidebarContent>

                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
