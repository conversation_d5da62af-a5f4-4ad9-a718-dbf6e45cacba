<?php

namespace App\Http\Controllers;

use App\Models\Assessment;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentResult;
use App\Models\SchoolClass;
use App\Models\Subject;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class AssessmentController extends Controller
{
    //
    public function index(Request $request, $class_id)
    {
        $class = SchoolClass::findOrFail($class_id);
        $subject = Subject::where('class_id', $class->id)->withCount('assessments')->first();

        if (!$subject) {
            return redirect()->route('subjects.index', ['class_id' => $class_id])->with('error', 'No subject found for this class. Please create a subject first.');
        }

        if ($request->has('subject_id') && $request->subject_id !== null) {
            $subject = Subject::findOrFail($request->subject_id);
        }
        $subjects = Subject::where('class_id', $class->id)->withCount('assessments')->get();

        $assessments = Assessment::where('subject_id', $subject->id)
            ->latest()
            ->withCount(['questions', 'results'])
            ->paginate(10);

        return Inertia::render('assessments/Index', [
            'assessments' => $assessments,
            'subjects' => $subjects,
            'subject' => $subject,
            'class' => $class,
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
        ]);

        $assessment = Assessment::create([
            'title' => $request->title,
            'description' => $request->description,
            'class_id' => $request->class_id,
            'subject_id' => $request->subject_id,
        ]);

        return redirect()->back()->with('success', 'Assessment created successfully');
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
        ]);

        $assessment = Assessment::findOrFail($id);
        $assessment->update([
            'title' => $request->title,
            'description' => $request->description,
        ]);

        return redirect()->back()->with('success', 'Assessment updated successfully');
    }

    public function destroy($id)
    {
        $assessment = Assessment::findOrFail($id);
        $assessment->delete();
        return redirect()->back()->with('success', 'Assessment deleted successfully');
    }

    // Question Management
    public function questions($id)
    {
        $assessment = Assessment::with(['questions' => function ($query) {
            $query->orderBy('id', 'asc');
        }])->findOrFail($id);

        return Inertia::render('assessments/Questions', [
            'assessment' => $assessment,
            'questions' => $assessment->questions,
        ]);
    }

    public function storeQuestion(Request $request, $id)
    {
        $assessment = Assessment::findOrFail($id);

        $request->validate([
            'question' => 'required|string',
            'question_type' => 'required|in:text,image',
            'correct_answer' => 'required|in:A,B,C,D',
            'audio' => 'nullable|file|mimes:mp3,wav,ogg|max:10240', // 10MB max
        ]);

        $questionData = [
            'assessment_id' => $assessment->id,
            'question' => $request->question,
            'question_type' => $request->question_type,
            'correct_answer' => $request->correct_answer,
        ];

        // Handle audio file upload
        if ($request->hasFile('audio')) {
            $file = $request->file('audio');
            $path = $file->store('questions/audio', 'public');
            $questionData['audio_url'] = Storage::url($path);
        } elseif ($request->has('audio_url')) {
            $questionData['audio_url'] = $request->audio_url;
        }

        if ($request->question_type === 'text') {
            $request->validate([
                'option_a' => 'required|string',
                'option_b' => 'required|string',
                'option_c' => 'required|string',
                'option_d' => 'required|string',
            ]);

            $questionData['option_a'] = $request->option_a;
            $questionData['option_b'] = $request->option_b;
            $questionData['option_c'] = $request->option_c;
            $questionData['option_d'] = $request->option_d;
        } else {
            // For image type, handle file uploads
            foreach (['option_a', 'option_b', 'option_c', 'option_d'] as $option) {
                if ($request->hasFile($option)) {
                    $file = $request->file($option);
                    $path = $file->store('questions/options', 'public');
                    $questionData[$option] = Storage::url($path);
                }
            }
        }

        AssessmentQuestion::create($questionData);

        return redirect()->back()->with('success', 'Question added successfully');
    }

    public function updateQuestion(Request $request, $assessmentId, $questionId)
    {
        $question = AssessmentQuestion::where('assessment_id', $assessmentId)
            ->where('id', $questionId)
            ->firstOrFail();

        $request->validate([
            'question' => 'required|string',
            'question_type' => 'required|in:text,image',
            'correct_answer' => 'required|in:A,B,C,D',
            'audio' => 'nullable|file|mimes:mp3,wav,ogg|max:10240', // 10MB max
        ]);

        $questionData = [
            'question' => $request->question,
            'question_type' => $request->question_type,
            'correct_answer' => $request->correct_answer,
        ];

        // Handle audio file upload
        if ($request->hasFile('audio')) {
            // Delete old audio file if exists
            if ($question->audio_url && str_starts_with($question->audio_url, '/storage/')) {
                $oldPath = str_replace('/storage/', 'public/', $question->audio_url);
                Storage::delete($oldPath);
            }

            $file = $request->file('audio');
            $path = $file->store('questions/audio', 'public');
            $questionData['audio_url'] = Storage::url($path);
        } elseif ($request->has('audio_url')) {
            $questionData['audio_url'] = $request->audio_url;
        }

        if ($request->question_type === 'text') {
            $request->validate([
                'option_a' => 'required|string',
                'option_b' => 'required|string',
                'option_c' => 'required|string',
                'option_d' => 'required|string',
            ]);

            $questionData['option_a'] = $request->option_a;
            $questionData['option_b'] = $request->option_b;
            $questionData['option_c'] = $request->option_c;
            $questionData['option_d'] = $request->option_d;
        } else {
            // For image type, handle file uploads
            foreach (['option_a', 'option_b', 'option_c', 'option_d'] as $option) {
                if ($request->hasFile($option)) {
                    // Delete old file if exists
                    if ($question->$option && str_starts_with($question->$option, '/storage/')) {
                        $oldPath = str_replace('/storage/', 'public/', $question->$option);
                        Storage::delete($oldPath);
                    }

                    $file = $request->file($option);
                    $path = $file->store('questions/options', 'public');
                    $questionData[$option] = Storage::url($path);
                }
            }
        }

        $question->update($questionData);

        return redirect()->back()->with('success', 'Question updated successfully');
    }

    public function destroyQuestion($assessmentId, $questionId)
    {
        $question = AssessmentQuestion::where('assessment_id', $assessmentId)
            ->where('id', $questionId)
            ->firstOrFail();

        // Delete image files if they exist
        if ($question->question_type === 'image') {
            foreach (['option_a', 'option_b', 'option_c', 'option_d'] as $option) {
                if ($question->$option && str_starts_with($question->$option, '/storage/')) {
                    $path = str_replace('/storage/', 'public/', $question->$option);
                    Storage::delete($path);
                }
            }
        }

        // Delete audio file if exists
        if ($question->audio_url && str_starts_with($question->audio_url, '/storage/')) {
            $path = str_replace('/storage/', 'public/', $question->audio_url);
            Storage::delete($path);
        }

        $question->delete();

        return redirect()->back()->with('success', 'Question deleted successfully');
    }

    // Assessment Results
    public function results($id)
    {
        $assessment = Assessment::withCount('questions')->findOrFail($id);

        // Get unique students who have attempted this assessment
        $studentIds = AssessmentResult::where('assessment_id', $assessment->id)
            ->where('progress', 'completed')
            ->distinct('student_id')
            ->pluck('student_id');

        // Calculate average results for each student
        $results = $studentIds->map(function ($studentId) use ($assessment) {
            $student = User::find($studentId);
            $averageData = $assessment->getStudentAverageResult($studentId);

            if (!$averageData) {
                return null;
            }

            $allAttempts = $assessment->getStudentAttempts($studentId);
            $bestAttempt = $allAttempts->sortByDesc('score')->first();
            $latestAttempt = $allAttempts->sortByDesc('created_at')->first();

            return [
                'id' => $latestAttempt->id, // Use latest attempt ID for detail links
                'assessment_id' => $assessment->id,
                'student_id' => $studentId,
                'student' => [
                    'id' => $student->id,
                    'name' => $student->name,
                    'email' => $student->email,
                ],
                'average_score' => $averageData['average_score'],
                'status' => $averageData['status'],
                'attempt_count' => $averageData['attempt_count'],
                'best_score' => $bestAttempt->score,
                'latest_score' => $latestAttempt->score,
                'created_at' => $latestAttempt->created_at,
                'updated_at' => $latestAttempt->updated_at,
            ];
        })->filter()->values();

        // Paginate the results manually
        $perPage = 10;
        $currentPage = request()->get('page', 1);
        $total = $results->count();
        $paginatedResults = $results->slice(($currentPage - 1) * $perPage, $perPage)->values();

        $paginationData = [
            'data' => $paginatedResults,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $currentPage,
            'last_page' => ceil($total / $perPage),
        ];

        return Inertia::render('assessments/Results', [
            'assessment' => $assessment,
            'results' => $paginationData,
        ]);
    }

    public function resultDetail($assessmentId, $resultId)
    {
        $assessment = Assessment::withCount('questions')->findOrFail($assessmentId);
        $result = AssessmentResult::with(['student', 'questionDetails' => function ($query) {
            $query->orderBy('id', 'asc');
        }])->findOrFail($resultId);

        // Format questions for the frontend
        $questions = $result->questionDetails->map(function ($detail) {
            $question = $detail->question;
            return [
                'id' => $question->id,
                'question' => $question->question,
                'question_type' => $question->question_type,
                'option_a' => $question->option_a,
                'option_b' => $question->option_b,
                'option_c' => $question->option_c,
                'option_d' => $question->option_d,
                'correct_answer' => $question->correct_answer,
                'audio_url' => $question->audio_url,
                'student_answer' => $detail->student_answer,
                'is_correct' => $detail->is_correct,
            ];
        });

        // Add questions to the result object
        $result->questions = $questions;

        return Inertia::render('assessments/ResultDetail', [
            'assessment' => $assessment,
            'result' => $result,
        ]);
    }
}
