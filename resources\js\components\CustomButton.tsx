import { cn } from '@/lib/utils';

interface CustomButtonProps {
    children: React.ReactNode;
    className?: string;
    onClick?: () => void;
    disabled?: boolean;
}

function CustomButton({ children, className, onClick, disabled }: CustomButtonProps) {
    return (
        <div
            className={cn(
                'flex cursor-pointer items-center gap-2 rounded-md bg-amber-500 px-4 py-2 text-base font-medium text-white',
                className,
                disabled && 'opacity-50',
            )}
            onClick={!disabled ? onClick : undefined}
        >
            {children}
        </div>
    );
}

export default CustomButton;
