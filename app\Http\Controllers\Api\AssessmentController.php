<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Assessment;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentQuestionDetail;
use App\Models\AssessmentResult;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AssessmentController extends Controller
{
    /**
     * Get a list of available assessments for the student
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAssessments($user_id, $subject_id)
    {

        $user = User::find($user_id);
        // Get assessments for student's class
        $assessments = Assessment::where('subject_id', $subject_id)
            ->with(['subject', 'unit', 'topic'])
            ->get()
            ->map(function ($assessment) use ($user) {
                // Check if student has already taken this assessment
                $result = AssessmentResult::where('assessment_id', $assessment->id)
                    ->where('student_id', $user->id)
                    ->first();

                $progress = $result ? $result->progress : 'not_started';
                $score = $result ? $result->score : null;

                return [
                    'id' => $assessment->id,
                    'title' => $assessment->title,
                    'description' => $assessment->description,
                    'subject' => $assessment->subject->name,
                    'unit' => $assessment->unit ? $assessment->unit->name : null,
                    'topic' => $assessment->topic ? $assessment->topic->name : null,
                    'progress' => $progress,
                    'score' => $score,
                    'status' => $result ? $result->status : null
                ];
            });

        return response()->json([
            'success' => true,
            'assessments' => $assessments
        ]);
    }

    /**
     * Get assessment details (without answers)
     * 
     * @param int $id Assessment ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAssessment($id, $user_id)
    {
        try {
            $user = User::find($user_id);

            $assessment = Assessment::with(['subject', 'unit', 'topic'])
                ->findOrFail($id);

            // Check if assessment belongs to student's class
            if ($assessment->class_id != $user->class_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have access to this assessment'
                ], 403);
            }

            // Check if student has completed attempts (for information purposes)
            $completedAttempts = AssessmentResult::where('assessment_id', $id)
                ->where('student_id', $user->id)
                ->where('progress', 'completed')
                ->count();

            $averageScore = null;
            if ($completedAttempts > 0) {
                $averageScore = AssessmentResult::getAverageScore($id, $user->id);
            }

            // Get questions without correct answers
            $questions = AssessmentQuestion::where('assessment_id', $id)
                ->get()
                ->map(function ($question) {
                    return [
                        'id' => $question->id,
                        'question' => $question->question,
                        'question_type' => $question->question_type,
                        'audio_url' => $question->audio_url,
                        'options' => [
                            'A' => $question->option_a,
                            'B' => $question->option_b,
                            'C' => $question->option_c,
                            'D' => $question->option_d,
                        ]
                    ];
                });

            $assessmentData = [
                'id' => $assessment->id,
                'title' => $assessment->title,
                'description' => $assessment->description,
                'subject' => $assessment->subject->name,
                'unit' => $assessment->unit ? $assessment->unit->name : null,
                'topic' => $assessment->topic ? $assessment->topic->name : null,
                'total_questions' => $questions->count(),
                'questions' => $questions
            ];

            // Check for any in-progress assessment result
            $inProgressResult = AssessmentResult::where('assessment_id', $id)
                ->where('student_id', $user->id)
                ->where('progress', 'started')
                ->first();

            if ($inProgressResult) {
                // Resume existing attempt
                $result = $inProgressResult;
            } else {
                // Create new attempt
                $attemptNumber = AssessmentResult::getNextAttemptNumber($id, $user->id);

                $result = AssessmentResult::create([
                    'assessment_id' => $id,
                    'student_id' => $user->id,
                    'attempt_number' => $attemptNumber,
                    'score' => 0,
                    'failed_questions' => 0,
                    'correct_questions' => 0,
                    'progress' => 'started'
                ]);
            }

            return response()->json([
                'success' => true,
                'assessment' => $assessmentData,
                'result_id' => $result->id,
                'attempt_info' => [
                    'current_attempt' => $result->attempt_number,
                    'completed_attempts' => $completedAttempts,
                    'average_score' => $averageScore ? round($averageScore, 2) : null,
                    'is_retake' => $completedAttempts > 0
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve assessment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Submit answers for an assessment
     * 
     * @param Request $request
     * @param int $id Assessment ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitAnswers(Request $request, $id)
    {
        try {
            $user = User::find($request->user_id);

            // Validate request
            $validator = Validator::make($request->all(), [
                'result_id' => 'required|exists:assessment_results,id',
                'answers' => 'required|array',
                'answers.*.question_id' => 'required|exists:assessment_questions,id',
                'answers.*.answer' => 'required|string|max:1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $assessment = Assessment::findOrFail($id);

            // Check if assessment belongs to student's class
            if ($assessment->class_id != $user->class_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have access to this assessment'
                ], 403);
            }

            $resultId = $request->input('result_id');
            $result = AssessmentResult::where('id', $resultId)
                ->where('student_id', $user->id)
                ->where('assessment_id', $id)
                ->first();

            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => 'Assessment result not found'
                ], 404);
            }

            if ($result->progress === 'completed') {
                return response()->json([
                    'success' => false,
                    'message' => 'This assessment has already been completed'
                ], 400);
            }

            // Process submitted answers
            $answers = $request->input('answers');
            $totalQuestions = AssessmentQuestion::where('assessment_id', $id)->count();
            $submittedQuestions = count($answers);

            if ($submittedQuestions < $totalQuestions) {
                return response()->json([
                    'success' => false,
                    'message' => 'All questions must be answered',
                    'answered' => $submittedQuestions,
                    'total' => $totalQuestions
                ], 400);
            }

            $correctCount = 0;
            $failedCount = 0;

            DB::beginTransaction();

            try {
                // Remove any existing question details if the student is resubmitting
                AssessmentQuestionDetail::where('assessment_result_id', $resultId)->delete();

                foreach ($answers as $answer) {
                    $questionId = $answer['question_id'];
                    $studentAnswer = strtoupper($answer['answer']);

                    $question = AssessmentQuestion::findOrFail($questionId);
                    $isCorrect = strtoupper($question->correct_answer) === $studentAnswer;

                    // Record question details
                    AssessmentQuestionDetail::create([
                        'assessment_result_id' => $resultId,
                        'assessment_question_id' => $questionId,
                        'student_answer' => $studentAnswer,
                        'is_correct' => $isCorrect
                    ]);

                    if ($isCorrect) {
                        $correctCount++;
                    } else {
                        $failedCount++;
                    }
                }

                // Calculate score (percentage)
                $score = ($correctCount / $totalQuestions) * 100;

                // Update result
                $status = $score >= 50 ? 'passed' : 'failed';
                $result->score = $score;
                $result->correct_questions = $correctCount;
                $result->failed_questions = $failedCount;
                $result->status = $status;
                $result->progress = 'completed';

                // Calculate time taken if we have a start time (for future enhancement)
                // For now, we'll leave time_taken as null

                $result->save();

                // Calculate average score across all attempts
                $averageScore = AssessmentResult::getAverageScore($result->assessment_id, $result->student_id);
                $averageStatus = $averageScore >= 50 ? 'passed' : 'failed';

                // Get attempt count
                $attemptCount = AssessmentResult::where('assessment_id', $result->assessment_id)
                    ->where('student_id', $result->student_id)
                    ->where('progress', 'completed')
                    ->count();

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Assessment completed successfully',
                    'result' => [
                        'score' => $score,
                        'correct_questions' => $correctCount,
                        'failed_questions' => $failedCount,
                        'status' => $status,
                        'passed' => $status === 'passed',
                        'progress' => 'completed',
                        'attempt_number' => $result->attempt_number,
                        'average_score' => round($averageScore, 2),
                        'average_status' => $averageStatus,
                        'average_passed' => $averageStatus === 'passed',
                        'attempt_count' => $attemptCount
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit assessment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get assessment result details
     *
     * @param int $id Assessment ID
     * @param int $user_id User ID
     * @param int|null $attempt_number Specific attempt number (optional)
     * @return \Illuminate\Http\JsonResponse
     */
    public function getResult($id, $user_id, $attempt_number = null)
    {
        try {
            $user = User::find($user_id);

            // If specific attempt requested, get that attempt
            if ($attempt_number) {
                $result = AssessmentResult::where('assessment_id', $id)
                    ->where('student_id', $user->id)
                    ->where('attempt_number', $attempt_number)
                    ->where('progress', 'completed')
                    ->with(['assessment.subject', 'assessment.topic', 'questionDetails.question'])
                    ->first();
            } else {
                // Get the most recent completed attempt
                $result = AssessmentResult::where('assessment_id', $id)
                    ->where('student_id', $user->id)
                    ->where('progress', 'completed')
                    ->with(['assessment.subject', 'assessment.topic', 'questionDetails.question'])
                    ->orderBy('attempt_number', 'desc')
                    ->first();
            }

            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => 'Assessment result not found'
                ], 404);
            }

            $assessment = $result->assessment;

            // Format questions with student answers
            $questions = $result->questionDetails->map(function ($detail) {
                $question = $detail->question;

                return [
                    'id' => $question->id,
                    'question' => $question->question,
                    'question_type' => $question->question_type,
                    'options' => [
                        'A' => $question->option_a,
                        'B' => $question->option_b,
                        'C' => $question->option_c,
                        'D' => $question->option_d,
                    ],
                    'correct_answer' => $question->correct_answer,
                    'audio_url' => $question->audio_url,
                    'student_answer' => $detail->student_answer,
                    'is_correct' => $detail->is_correct
                ];
            });

            // Get average score and attempt information
            $averageScore = AssessmentResult::getAverageScore($id, $user->id);
            $allAttempts = AssessmentResult::where('assessment_id', $id)
                ->where('student_id', $user->id)
                ->where('progress', 'completed')
                ->orderBy('attempt_number')
                ->get(['attempt_number', 'score', 'status', 'created_at']);

            $resultData = [
                'id' => $result->id,
                'assessment' => [
                    'id' => $assessment->id,
                    'title' => $assessment->title,
                    'description' => $assessment->description,
                    'subject' => $assessment->subject->name,
                    'topic' => $assessment->topic ? $assessment->topic->name : null,
                ],
                'current_attempt' => [
                    'attempt_number' => $result->attempt_number,
                    'score' => $result->score,
                    'correct_questions' => $result->correct_questions,
                    'failed_questions' => $result->failed_questions,
                    'status' => $result->status,
                    'passed' => $result->status === 'passed',
                    'completed_at' => $result->updated_at->format('Y-m-d H:i:s'),
                ],
                'average_score' => $averageScore ? round($averageScore, 2) : null,
                'average_status' => $averageScore ? ($averageScore >= 50 ? 'passed' : 'failed') : null,
                'average_passed' => $averageScore ? $averageScore >= 50 : false,
                'total_attempts' => $allAttempts->count(),
                'all_attempts' => $allAttempts->map(function ($attempt) {
                    return [
                        'attempt_number' => $attempt->attempt_number,
                        'score' => $attempt->score,
                        'status' => $attempt->status,
                        'completed_at' => $attempt->created_at->format('Y-m-d H:i:s')
                    ];
                }),
                'questions' => $questions
            ];

            return response()->json([
                'success' => true,
                'result' => $resultData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve assessment result: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get student's assessment statistics
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStudentStats($user_id)
    {
        try {
            $user = User::find($user_id);

            // Get all assessment results for the student
            $results = AssessmentResult::where('student_id', $user->id)->get();

            // Calculate statistics using unique assessments
            $totalAssessments = Assessment::where('class_id', $user->class_id)->count();

            // Get unique assessment attempts
            $uniqueAssessments = $results->groupBy('assessment_id');
            $attemptedCount = $uniqueAssessments->count();

            // Calculate completion and pass/fail based on average scores
            $completedAssessments = collect();
            $passedCount = 0;
            $failedCount = 0;
            $inProgressCount = $results->where('progress', 'started')->count();

            foreach ($uniqueAssessments as $assessmentId => $assessmentResults) {
                $completedResults = $assessmentResults->where('progress', 'completed');
                if ($completedResults->count() > 0) {
                    $avgScore = AssessmentResult::getAverageScore($assessmentId, $user->id);
                    if ($avgScore !== null) {
                        $completedAssessments->push($avgScore);
                        if ($avgScore >= 50) {
                            $passedCount++;
                        } else {
                            $failedCount++;
                        }
                    }
                }
            }

            $completedCount = $completedAssessments->count();
            $avgScore = $completedAssessments->isEmpty() ? 0 : $completedAssessments->avg();

            // Calculate completion rate
            $completionRate = $totalAssessments > 0 ? ($completedCount / $totalAssessments) * 100 : 0;

            // Calculate pass rate
            $passRate = $completedCount > 0 ? ($passedCount / $completedCount) * 100 : 0;

            // Get recent assessments with average scores
            $recentAssessmentIds = AssessmentResult::where('student_id', $user->id)
                ->where('progress', 'completed')
                ->distinct('assessment_id')
                ->orderBy('updated_at', 'desc')
                ->take(5)
                ->pluck('assessment_id');

            $recentAssessments = collect();
            foreach ($recentAssessmentIds as $assessmentId) {
                $assessment = Assessment::with('subject')->find($assessmentId);
                if ($assessment) {
                    $avgScore = AssessmentResult::getAverageScore($assessmentId, $user->id);
                    $latestResult = AssessmentResult::where('assessment_id', $assessmentId)
                        ->where('student_id', $user->id)
                        ->where('progress', 'completed')
                        ->latest()
                        ->first();

                    if ($avgScore !== null && $latestResult) {
                        $recentAssessments->push([
                            'id' => $assessment->id,
                            'title' => $assessment->title,
                            'subject' => $assessment->subject->name,
                            'score' => round($avgScore, 0),
                            'status' => $avgScore >= 50 ? 'passed' : 'failed',
                            'progress' => 'completed',
                            'completed_at' => $latestResult->updated_at->format('Y-m-d H:i:s')
                        ]);
                    }
                }
            }

            // Get subjects and their average scores using student averages
            $subjectPerformance = [];
            if ($completedCount > 0) {
                $subjectAssessments = AssessmentResult::where('student_id', $user->id)
                    ->where('progress', 'completed')
                    ->with('assessment.subject')
                    ->get()
                    ->groupBy(function ($result) {
                        return $result->assessment->subject->name;
                    });

                foreach ($subjectAssessments as $subjectName => $results) {
                    $assessmentIds = $results->pluck('assessment_id')->unique();
                    $subjectAverages = collect();

                    foreach ($assessmentIds as $assessmentId) {
                        $avgScore = AssessmentResult::getAverageScore($assessmentId, $user->id);
                        if ($avgScore !== null) {
                            $subjectAverages->push($avgScore);
                        }
                    }

                    if ($subjectAverages->count() > 0) {
                        $subjectPerformance[] = [
                            'subject' => $subjectName,
                            'avgScore' => round($subjectAverages->avg(), 0),
                            'assessmentCount' => $assessmentIds->count()
                        ];
                    }
                }
            }

            return response()->json([
                'success' => true,
                'stats' => [
                    'totalAssignedAssessments' => $totalAssessments,
                    'attemptedAssessments' => $attemptedCount,
                    'completedAssessments' => $completedCount,
                    'passedAssessments' => $passedCount,
                    'failedAssessments' => $failedCount,
                    'inProgressAssessments' => $inProgressCount,
                    'averageScore' => round($avgScore, 0),
                    'completionRate' => round($completionRate, 0),
                    'passRate' => round($passRate, 0),
                    'recentAssessments' => $recentAssessments,
                    'subjectPerformance' => $subjectPerformance
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve assessment statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get detailed attempt history for a student-assessment pair
     *
     * @param int $assessmentId Assessment ID
     * @param int $userId User ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAttemptHistory($assessmentId, $userId)
    {
        try {
            $user = User::find($userId);
            $assessment = Assessment::findOrFail($assessmentId);

            $attempts = AssessmentResult::where('assessment_id', $assessmentId)
                ->where('student_id', $user->id)
                ->where('progress', 'completed')
                ->with(['questionDetails.question'])
                ->orderBy('attempt_number')
                ->get();

            if ($attempts->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No completed attempts found'
                ], 404);
            }

            $averageScore = AssessmentResult::getAverageScore($assessmentId, $user->id);

            $attemptHistory = $attempts->map(function ($attempt) {
                return [
                    'attempt_number' => $attempt->attempt_number,
                    'score' => $attempt->score,
                    'correct_questions' => $attempt->correct_questions,
                    'failed_questions' => $attempt->failed_questions,
                    'status' => $attempt->status,
                    'passed' => $attempt->status === 'passed',
                    'completed_at' => $attempt->updated_at->format('Y-m-d H:i:s'),
                    'is_best_attempt' => $attempt->isBestAttempt(),
                    'question_count' => $attempt->questionDetails->count()
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'assessment' => [
                        'id' => $assessment->id,
                        'title' => $assessment->title,
                        'subject' => $assessment->subject->name,
                    ],
                    'student' => [
                        'id' => $user->id,
                        'name' => $user->name,
                    ],
                    'average_score' => round($averageScore, 2),
                    'average_status' => $averageScore >= 50 ? 'passed' : 'failed',
                    'total_attempts' => $attempts->count(),
                    'attempts' => $attemptHistory
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve attempt history: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if student can retake assessment
     *
     * @param int $assessmentId Assessment ID
     * @param int $userId User ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function canRetake($assessmentId, $userId)
    {
        try {
            $user = User::find($userId);

            // Check if there's an in-progress attempt
            $inProgress = AssessmentResult::where('assessment_id', $assessmentId)
                ->where('student_id', $user->id)
                ->where('progress', 'started')
                ->exists();

            if ($inProgress) {
                return response()->json([
                    'success' => true,
                    'can_retake' => false,
                    'reason' => 'Assessment is currently in progress'
                ]);
            }

            // For now, unlimited retakes are allowed
            $attemptCount = AssessmentResult::where('assessment_id', $assessmentId)
                ->where('student_id', $user->id)
                ->where('progress', 'completed')
                ->count();

            return response()->json([
                'success' => true,
                'can_retake' => true,
                'current_attempts' => $attemptCount,
                'next_attempt_number' => $attemptCount + 1
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check retake eligibility: ' . $e->getMessage()
            ], 500);
        }
    }
}
