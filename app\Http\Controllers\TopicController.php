<?php

namespace App\Http\Controllers;

use App\Models\SchoolClass;
use App\Models\Subject;
use App\Models\Topic;
use App\Models\Unit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class TopicController extends Controller
{
    /**
     * Display a listing of the topics for a unit.
     */
    public function __construct()
    {
        abort_if(Auth::user()->role !== 'admin', 403);
    }
    public function index(Request $request, $class_id)
    {
        $class = SchoolClass::findOrFail($class_id);
        $subject = Subject::where('class_id', $class->id)->first();

        if ($request->has('subject_id') && $request->subject_id !== null) {
            $subject = Subject::findOrFail($request->subject_id);
        }
        $unit_id = Unit::where('subject_id', $subject->id)->first();
        if (!$unit_id) {
            return redirect()->route('units.index', ['class_id' => $class_id, 'subject_id' => $subject->id])->with('error', 'No unit found for ' . $subject->name . '. Please create a unit first.');
        }
        if ($request->has('unit_id') && $request->unit_id !== null) {
            $unit_id = Unit::findOrFail($request->unit_id);
        }

        $topics = Topic::where('unit_id', $unit_id->id)->latest()->paginate(10) ?? [];
        $subjects = Subject::where('class_id', $class->id)->get();
        $unit = Unit::findOrFail($unit_id->id);

        $units = Unit::where('subject_id', $subject->id)->get();


        return Inertia::render('topics/Index', [
            'units' => $units,
            'subjects' => $subjects,
            'subject' => $subject,
            'class' => $class,
            'topics' => $topics,
            'unit' => $unit,
        ]);
    }

    /**
     * Store a newly created topic in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'unit_id' => 'required|exists:units,id',
        ]);

        Topic::create([
            'name' => $validated['name'],
            'unit_id' => $validated['unit_id'],

        ]);

        return redirect()->back()->with('success', 'Topic created successfully');
    }

    /**
     * Update the specified topic in storage.
     */
    public function update(Request $request, $id)
    {
        $topic = Topic::findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $topic->update([
            'name' => $validated['name'],
        ]);

        return redirect()->back()->with('success', 'Topic updated successfully');
    }

    /**
     * Remove the specified topic from storage.
     */
    public function destroy($id)
    {
        $topic = Topic::findOrFail($id);
        $topic->delete();

        return redirect()->back()->with('success', 'Topic deleted successfully');
    }

    /**
     * Reorder topics.
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'topics' => 'required|array',
            'topics.*.id' => 'required|exists:topics,id',
            'topics.*.order' => 'required|integer|min:0',
        ]);

        foreach ($request->topics as $topicData) {
            Topic::where('id', $topicData['id'])->update(['order' => $topicData['order']]);
        }

        return response()->json(['message' => 'Topics reordered successfully']);
    }

    /**
     * Display the topic content editor.
     */
    public function content(Request $request, $topicId)
    {
        $topic = Topic::with('contentBlocks')->findOrFail($topicId);
        $unit = Unit::findOrFail($topic->unit_id);
        $subject = Subject::findOrFail($unit->subject_id);
        $class = SchoolClass::findOrFail($subject->class_id);

        // Get content blocks with their children
        $contentBlocks = $topic->contentBlocks()
            ->whereNull('parent_id')
            ->with('children')
            ->orderBy('order')
            ->get();

        return Inertia::render('topics/Content', [
            'topic' => $topic,
            'contentBlocks' => $contentBlocks,
            'unit' => $unit,
            'subject' => $subject,
            'class' => $class,
        ]);
    }

    /**
     * Display the topic content preview.
     */
    public function preview(Request $request, $topicId)
    {
        $topic = Topic::with('contentBlocks')->findOrFail($topicId);
        $unit = Unit::findOrFail($topic->unit_id);
        $subject = Subject::findOrFail($unit->subject_id);
        $class = SchoolClass::findOrFail($subject->class_id);

        // Get content blocks with their children
        $contentBlocks = $topic->contentBlocks()
            ->whereNull('parent_id')
            ->with('children')
            ->orderBy('order')
            ->get();

        return Inertia::render('topics/Preview', [
            'topic' => $topic,
            'contentBlocks' => $contentBlocks,
            'unit' => $unit,
            'subject' => $subject,
            'class' => $class,
        ]);
    }
}
