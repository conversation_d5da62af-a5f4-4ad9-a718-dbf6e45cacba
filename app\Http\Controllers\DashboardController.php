<?php

namespace App\Http\Controllers;

use App\Models\Assessment;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentResult;
use App\Models\CompletedTopic;
use App\Models\ContentBlock;
use App\Models\Lesson;
use App\Models\SchoolClass;
use App\Models\Subject;
use App\Models\Topic;
use App\Models\Unit;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        try {
            // Fetch classes and subjects
            $classes = SchoolClass::select('id', 'name')->get();
            $subjects = Subject::select('id', 'name', 'class_id')->get();

            // Fetch assessment statistics
            $assessmentStats = $this->getAssessmentStats();

            // Fetch student statistics
            $studentStats = $this->getStudentStats();

            // Fetch content statistics
            $contentStats = $this->getContentStats();

            // Fetch lesson statistics
            $lessonStats = $this->getLessonStats();

            // Fetch teacher statistics
            $teacherStats = $this->getTeacherStats();

            // Fetch chart data
            $performanceBySubject = $this->getPerformanceBySubject();
            $assessmentCompletionData = $this->getAssessmentCompletionData();
            $contentTypeDistribution = $this->getContentTypeDistribution();
            $studentProgressData = $this->getStudentProgressData();
            $lessonCompletionTrends = $this->getLessonCompletionTrends();
            $classPerformanceComparison = $this->getClassPerformanceComparison();

            // Fetch recent assessments
            $recentAssessments = $this->getRecentAssessments();

            // Fetch recent activities
            $recentActivities = $this->getRecentActivities();

            // Fetch alerts and notifications
            $alerts = $this->getAlerts();

            return Inertia::render('dashboard', [
                'classes' => $classes,
                'subjects' => $subjects,
                'assessmentStats' => $assessmentStats,
                'studentStats' => $studentStats,
                'contentStats' => $contentStats,
                'lessonStats' => $lessonStats,
                'teacherStats' => $teacherStats,
                'performanceBySubject' => $performanceBySubject,
                'assessmentCompletionData' => $assessmentCompletionData,
                'contentTypeDistribution' => $contentTypeDistribution,
                'studentProgressData' => $studentProgressData,
                'lessonCompletionTrends' => $lessonCompletionTrends,
                'classPerformanceComparison' => $classPerformanceComparison,
                'recentAssessments' => $recentAssessments,
                'recentActivities' => $recentActivities,
                'alerts' => $alerts,
            ]);
        } catch (QueryException $e) {
            Log::error('Dashboard query error: ' . $e->getMessage());

            // Provide default data structure for development
            return $this->renderDashboardWithDefaults();
        } catch (Exception $e) {
            Log::error('Dashboard error: ' . $e->getMessage());

            // Provide default data structure for development
            return $this->renderDashboardWithDefaults();
        }
    }

    /**
     * Render dashboard with default data (for development)
     */
    private function renderDashboardWithDefaults()
    {
        // Create default data for development
        $classes = [
            ['id' => 1, 'name' => 'Class 1'],
            ['id' => 2, 'name' => 'Class 2'],
            ['id' => 3, 'name' => 'Class 3'],
        ];

        $subjects = [
            ['id' => 1, 'name' => 'Mathematics', 'class_id' => 1],
            ['id' => 2, 'name' => 'Science', 'class_id' => 1],
            ['id' => 3, 'name' => 'English', 'class_id' => 2],
            ['id' => 4, 'name' => 'History', 'class_id' => 2],
            ['id' => 5, 'name' => 'Geography', 'class_id' => 3],
        ];

        $assessmentStats = [
            'total' => 24,
            'activeToday' => 5,
            'avgScore' => 72,
            'completionRate' => 68,
        ];

        $studentStats = [
            'total' => 150,
            'activeToday' => 87,
            'averagePerformance' => 76,
        ];

        $contentStats = [
            'totalTopics' => 48,
            'totalUnits' => 12,
            'totalContentBlocks' => 346,
        ];

        $performanceBySubject = [
            ['subject' => 'Mathematics', 'avgScore' => 68],
            ['subject' => 'Science', 'avgScore' => 75],
            ['subject' => 'English', 'avgScore' => 82],
            ['subject' => 'History', 'avgScore' => 77],
            ['subject' => 'Geography', 'avgScore' => 71],
        ];

        $assessmentCompletionData = [
            ['date' => 'Mon', 'completed' => 12, 'started' => 18],
            ['date' => 'Tue', 'completed' => 15, 'started' => 20],
            ['date' => 'Wed', 'completed' => 18, 'started' => 22],
            ['date' => 'Thu', 'completed' => 14, 'started' => 19],
            ['date' => 'Fri', 'completed' => 16, 'started' => 20],
            ['date' => 'Sat', 'completed' => 8, 'started' => 10],
            ['date' => 'Sun', 'completed' => 5, 'started' => 8],
        ];

        $contentTypeDistribution = [
            ['type' => 'Text', 'count' => 145],
            ['type' => 'Image', 'count' => 78],
            ['type' => 'Video', 'count' => 42],
            ['type' => 'Audio', 'count' => 28],
            ['type' => 'List', 'count' => 53],
        ];

        $recentAssessments = [
            ['id' => 1, 'title' => 'Algebra Test', 'subject' => 'Mathematics', 'class' => 'Class 1', 'submissions' => 28, 'avgScore' => 72, 'date' => '2023-08-01'],
            ['id' => 2, 'title' => 'Scientific Method', 'subject' => 'Science', 'class' => 'Class 1', 'submissions' => 25, 'avgScore' => 78, 'date' => '2023-08-03'],
            ['id' => 3, 'title' => 'Grammar Quiz', 'subject' => 'English', 'class' => 'Class 2', 'submissions' => 32, 'avgScore' => 85, 'date' => '2023-08-05'],
            ['id' => 4, 'title' => 'World War II', 'subject' => 'History', 'class' => 'Class 2', 'submissions' => 27, 'avgScore' => 76, 'date' => '2023-08-07'],
        ];

        $lessonStats = [
            'totalLessons' => 156,
            'publishedLessons' => 142,
            'completionRate' => 78,
            'averageProgress' => 78,
        ];

        $teacherStats = [
            'totalTeachers' => 12,
            'activeToday' => 8,
            'avgContentCreated' => 13.2,
            'engagementRate' => 67,
        ];

        $studentProgressData = [
            ['class' => 'Class 1', 'totalStudents' => 45, 'averageProgress' => 82.5, 'completedTopics' => 156],
            ['class' => 'Class 2', 'totalStudents' => 38, 'averageProgress' => 76.3, 'completedTopics' => 134],
            ['class' => 'Class 3', 'totalStudents' => 42, 'averageProgress' => 79.1, 'completedTopics' => 148],
        ];

        $lessonCompletionTrends = [
            ['date' => 'Jan 15', 'completions' => 23, 'dayOfWeek' => 'Mon'],
            ['date' => 'Jan 16', 'completions' => 31, 'dayOfWeek' => 'Tue'],
            ['date' => 'Jan 17', 'completions' => 28, 'dayOfWeek' => 'Wed'],
            ['date' => 'Jan 18', 'completions' => 35, 'dayOfWeek' => 'Thu'],
            ['date' => 'Jan 19', 'completions' => 42, 'dayOfWeek' => 'Fri'],
            ['date' => 'Jan 20', 'completions' => 18, 'dayOfWeek' => 'Sat'],
            ['date' => 'Jan 21', 'completions' => 12, 'dayOfWeek' => 'Sun'],
        ];

        $classPerformanceComparison = [
            ['class' => 'Class 1', 'averageScore' => 78.5, 'totalAssessments' => 156, 'completionRate' => 92.3],
            ['class' => 'Class 2', 'averageScore' => 82.1, 'totalAssessments' => 134, 'completionRate' => 88.7],
            ['class' => 'Class 3', 'averageScore' => 75.8, 'totalAssessments' => 148, 'completionRate' => 90.1],
        ];

        $recentActivities = [
            ['type' => 'assessment_submission', 'title' => 'Assessment Submitted', 'description' => 'John Doe completed Math Quiz 1', 'score' => 85, 'timestamp' => now()->subMinutes(15), 'icon' => 'FileText'],
            ['type' => 'topic_completion', 'title' => 'Topic Completed', 'description' => 'Jane Smith completed Algebra Basics', 'timestamp' => now()->subMinutes(32), 'icon' => 'CheckCircle'],
            ['type' => 'assessment_submission', 'title' => 'Assessment Submitted', 'description' => 'Mike Johnson completed Science Test', 'score' => 92, 'timestamp' => now()->subHours(1), 'icon' => 'FileText'],
        ];

        $alerts = [
            ['type' => 'warning', 'title' => 'Low Performance Alert', 'message' => '5 students scored below 50% this week', 'action' => 'View Details', 'priority' => 'high'],
            ['type' => 'info', 'title' => 'Incomplete Assessments', 'message' => '12 assessments started but not completed', 'action' => 'Send Reminders', 'priority' => 'medium'],
        ];

        return Inertia::render('dashboard', [
            'classes' => $classes,
            'subjects' => $subjects,
            'assessmentStats' => $assessmentStats,
            'studentStats' => $studentStats,
            'contentStats' => $contentStats,
            'lessonStats' => $lessonStats,
            'teacherStats' => $teacherStats,
            'performanceBySubject' => $performanceBySubject,
            'assessmentCompletionData' => $assessmentCompletionData,
            'contentTypeDistribution' => $contentTypeDistribution,
            'studentProgressData' => $studentProgressData,
            'lessonCompletionTrends' => $lessonCompletionTrends,
            'classPerformanceComparison' => $classPerformanceComparison,
            'recentAssessments' => $recentAssessments,
            'recentActivities' => $recentActivities,
            'alerts' => $alerts,
        ]);
    }

    /**
     * Get assessment statistics
     */
    private function getAssessmentStats()
    {
        try {
            $total = Assessment::count();
            $activeToday = Assessment::whereDate('created_at', Carbon::today())->count();

            // Calculate average score using student averages (not individual attempts)
            $studentAverages = collect();
            $uniqueStudentAssessments = AssessmentResult::select('assessment_id', 'student_id')
                ->where('progress', 'completed')
                ->distinct()
                ->get();

            foreach ($uniqueStudentAssessments as $combo) {
                $avgScore = AssessmentResult::getAverageScore($combo->assessment_id, $combo->student_id);
                if ($avgScore !== null) {
                    $studentAverages->push($avgScore);
                }
            }

            $avgScore = $studentAverages->count() > 0 ? $studentAverages->avg() : 0;

            // Calculate completion rate (unique student-assessment pairs completed / total started)
            $startedCount = AssessmentResult::select('assessment_id', 'student_id')->distinct()->count();
            $completedCount = AssessmentResult::where('progress', 'completed')
                ->select('assessment_id', 'student_id')
                ->distinct()
                ->count();
            $completionRate = $startedCount > 0 ? ($completedCount / $startedCount) * 100 : 0;

            return [
                'total' => $total,
                'activeToday' => $activeToday,
                'avgScore' => round($avgScore, 0),
                'completionRate' => round($completionRate, 0),
            ];
        } catch (Exception $e) {
            Log::error('Error fetching assessment stats: ' . $e->getMessage());
            return [
                'total' => 0,
                'activeToday' => 0,
                'avgScore' => 0,
                'completionRate' => 0,
            ];
        }
    }

    /**
     * Get student statistics
     */
    private function getStudentStats()
    {
        try {
            $total = User::where('role', 'student')->count();
            $activeToday = User::where('role', 'student')
                ->whereDate('last_login_at', Carbon::today())
                ->count();

            // Calculate average performance using student averages across assessments
            $students = User::where('role', 'student')->get();
            $studentAverages = collect();

            foreach ($students as $student) {
                $assessmentIds = AssessmentResult::where('student_id', $student->id)
                    ->where('progress', 'completed')
                    ->distinct('assessment_id')
                    ->pluck('assessment_id');

                $studentAssessmentAverages = collect();
                foreach ($assessmentIds as $assessmentId) {
                    $avgScore = AssessmentResult::getAverageScore($assessmentId, $student->id);
                    if ($avgScore !== null) {
                        $studentAssessmentAverages->push($avgScore);
                    }
                }

                if ($studentAssessmentAverages->count() > 0) {
                    $studentAverages->push($studentAssessmentAverages->avg());
                }
            }

            $avgPerformance = $studentAverages->count() > 0 ? $studentAverages->avg() : 0;

            return [
                'total' => $total,
                'activeToday' => $activeToday,
                'averagePerformance' => round($avgPerformance, 0),
            ];
        } catch (Exception $e) {
            Log::error('Error fetching student stats: ' . $e->getMessage());
            return [
                'total' => 0,
                'activeToday' => 0,
                'averagePerformance' => 0,
            ];
        }
    }

    /**
     * Get content statistics
     */
    private function getContentStats()
    {
        try {
            $totalTopics = Topic::count();
            $totalUnits = Unit::count();
            $totalContentBlocks = ContentBlock::count();

            return [
                'totalTopics' => $totalTopics,
                'totalUnits' => $totalUnits,
                'totalContentBlocks' => $totalContentBlocks,
            ];
        } catch (Exception $e) {
            Log::error('Error fetching content stats: ' . $e->getMessage());
            return [
                'totalTopics' => 0,
                'totalUnits' => 0,
                'totalContentBlocks' => 0,
            ];
        }
    }

    /**
     * Get performance by subject data for chart
     */
    private function getPerformanceBySubject()
    {
        try {
            $subjects = Subject::with(['topics.assessments'])
                ->get()
                ->map(function ($subject) {
                    $studentAverages = collect();

                    foreach ($subject->topics as $topic) {
                        foreach ($topic->assessments as $assessment) {
                            // Get unique students who completed this assessment
                            $studentIds = AssessmentResult::where('assessment_id', $assessment->id)
                                ->where('progress', 'completed')
                                ->distinct('student_id')
                                ->pluck('student_id');

                            foreach ($studentIds as $studentId) {
                                $avgScore = AssessmentResult::getAverageScore($assessment->id, $studentId);
                                if ($avgScore !== null) {
                                    $studentAverages->push($avgScore);
                                }
                            }
                        }
                    }

                    $avgScore = $studentAverages->count() > 0 ? $studentAverages->avg() : 0;

                    return [
                        'subject' => $subject->name,
                        'avgScore' => round($avgScore, 0),
                    ];
                });

            return $subjects;
        } catch (Exception $e) {
            Log::error('Error fetching performance by subject: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get assessment completion data for the last 7 days
     */
    private function getAssessmentCompletionData()
    {
        try {
            $data = [];
            $days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

            // Get last 7 days data
            for ($i = 6; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $dayName = $days[$date->dayOfWeek - 1]; // 1-based in Carbon, 0-based in our array

                $started = AssessmentResult::whereDate('created_at', $date)->count();
                $completed = AssessmentResult::whereDate('created_at', $date)
                    ->where('status', 'completed')
                    ->count();

                $data[] = [
                    'date' => $dayName,
                    'started' => $started,
                    'completed' => $completed,
                ];
            }

            return $data;
        } catch (Exception $e) {
            Log::error('Error fetching assessment completion data: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get content type distribution for chart
     */
    private function getContentTypeDistribution()
    {
        try {
            $types = ['text', 'image', 'video', 'audio', 'list'];
            $data = [];

            foreach ($types as $type) {
                $count = ContentBlock::where('type', $type)->count();

                if ($count > 0) {
                    $data[] = [
                        'type' => ucfirst($type),
                        'count' => $count,
                    ];
                }
            }

            return $data;
        } catch (Exception $e) {
            Log::error('Error fetching content type distribution: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recent assessments with stats
     */
    private function getRecentAssessments()
    {
        try {
            return Assessment::with(['topic.subject.schoolClass', 'results'])
                ->latest()
                ->take(5)
                ->get()
                ->map(function ($assessment) {
                    $submissions = $assessment->results->count();
                    $avgScore = $submissions > 0 ? $assessment->results->avg('score') : 0;

                    return [
                        'id' => $assessment->id,
                        'title' => $assessment->name,
                        'subject' => $assessment->topic->subject->name,
                        'class' => $assessment->topic->subject->schoolClass->name,
                        'submissions' => $submissions,
                        'avgScore' => round($avgScore, 0),
                        'date' => $assessment->created_at->format('Y-m-d'),
                    ];
                });
        } catch (Exception $e) {
            Log::error('Error fetching recent assessments: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Filter dashboard data by class, subject, and date range
     */
    public function filter(Request $request)
    {
        try {
            $classId = $request->input('class_id', 'all');
            $subjectId = $request->input('subject_id', 'all');
            $dateFrom = $request->input('date_from', Carbon::now()->subDays(30)->format('Y-m-d'));
            $dateTo = $request->input('date_to', Carbon::now()->format('Y-m-d'));

            // Example of filtering assessments by class
            $query = Assessment::with(['topic.subject.schoolClass', 'results']);

            if ($classId !== 'all') {
                $query->whereHas('topic.subject.schoolClass', function ($q) use ($classId) {
                    $q->where('id', $classId);
                });
            }

            if ($subjectId !== 'all') {
                $query->whereHas('topic.subject', function ($q) use ($subjectId) {
                    $q->where('id', $subjectId);
                });
            }

            $filteredAssessments = $query->whereBetween('created_at', [$dateFrom, $dateTo])
                ->latest()
                ->take(5)
                ->get()
                ->map(function ($assessment) {
                    // Get unique students who completed this assessment
                    $studentIds = AssessmentResult::where('assessment_id', $assessment->id)
                        ->where('progress', 'completed')
                        ->distinct('student_id')
                        ->pluck('student_id');

                    $submissions = $studentIds->count();

                    // Calculate average using student averages
                    $studentAverages = collect();
                    foreach ($studentIds as $studentId) {
                        $avgScore = AssessmentResult::getAverageScore($assessment->id, $studentId);
                        if ($avgScore !== null) {
                            $studentAverages->push($avgScore);
                        }
                    }

                    $avgScore = $studentAverages->count() > 0 ? $studentAverages->avg() : 0;

                    return [
                        'id' => $assessment->id,
                        'title' => $assessment->title,
                        'subject' => $assessment->subject->name,
                        'class' => $assessment->class->name,
                        'submissions' => $submissions,
                        'avgScore' => round($avgScore, 0),
                        'date' => $assessment->created_at->format('Y-m-d'),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'recentAssessments' => $filteredAssessments,
                ]
            ]);
        } catch (Exception $e) {
            Log::error('Error filtering dashboard data: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while filtering dashboard data'
            ], 500);
        }
    }

    /**
     * Get lesson statistics
     */
    private function getLessonStats()
    {
        try {
            $totalLessons = Lesson::count();
            $publishedLessons = Lesson::where('is_published', true)->count();

            // Calculate lesson completion rate based on completed topics
            $totalTopics = Topic::count();
            $completedTopics = CompletedTopic::distinct('topic_id')->count();
            $completionRate = $totalTopics > 0 ? ($completedTopics / $totalTopics) * 100 : 0;

            return [
                'totalLessons' => $totalLessons,
                'publishedLessons' => $publishedLessons,
                'completionRate' => round($completionRate, 0),
                'averageProgress' => round($completionRate, 0), // Same as completion rate for now
            ];
        } catch (Exception $e) {
            Log::error('Error fetching lesson stats: ' . $e->getMessage());
            return [
                'totalLessons' => 0,
                'publishedLessons' => 0,
                'completionRate' => 0,
                'averageProgress' => 0,
            ];
        }
    }

    /**
     * Get teacher statistics
     */
    private function getTeacherStats()
    {
        try {
            $totalTeachers = User::where('role', 'teacher')->count();
            $activeTeachers = User::where('role', 'teacher')
                ->whereDate('last_login_at', Carbon::today())
                ->count();

            // Calculate average content creation (lessons + assessments per teacher)
            $totalContent = Lesson::count() + Assessment::count();
            $avgContentPerTeacher = $totalTeachers > 0 ? $totalContent / $totalTeachers : 0;

            return [
                'totalTeachers' => $totalTeachers,
                'activeToday' => $activeTeachers,
                'avgContentCreated' => round($avgContentPerTeacher, 1),
                'engagementRate' => $totalTeachers > 0 ? round(($activeTeachers / $totalTeachers) * 100, 0) : 0,
            ];
        } catch (Exception $e) {
            Log::error('Error fetching teacher stats: ' . $e->getMessage());
            return [
                'totalTeachers' => 0,
                'activeToday' => 0,
                'avgContentCreated' => 0,
                'engagementRate' => 0,
            ];
        }
    }

    /**
     * Get student progress data for charts
     */
    private function getStudentProgressData()
    {
        try {
            $classes = SchoolClass::with(['students' => function ($query) {
                $query->where('role', 'student');
            }])->get();

            $progressData = $classes->map(function ($class) {
                $students = $class->students;
                $totalStudents = $students->count();

                if ($totalStudents === 0) {
                    return [
                        'class' => $class->name,
                        'totalStudents' => 0,
                        'averageProgress' => 0,
                        'completedTopics' => 0,
                    ];
                }

                // Calculate average progress for this class
                $totalCompletedTopics = CompletedTopic::whereIn('user_id', $students->pluck('id'))->count();
                $totalTopics = Topic::count();
                $averageProgress = $totalTopics > 0 ? ($totalCompletedTopics / ($totalStudents * $totalTopics)) * 100 : 0;

                return [
                    'class' => $class->name,
                    'totalStudents' => $totalStudents,
                    'averageProgress' => round($averageProgress, 1),
                    'completedTopics' => $totalCompletedTopics,
                ];
            });

            return $progressData;
        } catch (Exception $e) {
            Log::error('Error fetching student progress data: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get lesson completion trends for charts
     */
    private function getLessonCompletionTrends()
    {
        try {
            $trends = [];
            $startDate = Carbon::now()->subDays(6);

            for ($i = 0; $i < 7; $i++) {
                $date = $startDate->copy()->addDays($i);
                $completions = CompletedTopic::whereDate('created_at', $date)->count();

                $trends[] = [
                    'date' => $date->format('M d'),
                    'completions' => $completions,
                    'dayOfWeek' => $date->format('D'),
                ];
            }

            return $trends;
        } catch (Exception $e) {
            Log::error('Error fetching lesson completion trends: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get class performance comparison data
     */
    private function getClassPerformanceComparison()
    {
        try {
            $classes = SchoolClass::with(['students' => function ($query) {
                $query->where('role', 'student');
            }])->get();

            $comparison = $classes->map(function ($class) {
                $studentIds = $class->students->pluck('id');

                if ($studentIds->isEmpty()) {
                    return [
                        'class' => $class->name,
                        'averageScore' => 0,
                        'totalAssessments' => 0,
                        'completionRate' => 0,
                    ];
                }

                // Calculate average scores using student averages
                $studentAverages = collect();
                $totalUniqueAssessments = 0;
                $completedUniqueAssessments = 0;

                foreach ($studentIds as $studentId) {
                    $assessmentIds = AssessmentResult::where('student_id', $studentId)
                        ->distinct('assessment_id')
                        ->pluck('assessment_id');

                    $totalUniqueAssessments += $assessmentIds->count();

                    $studentAssessmentAverages = collect();
                    foreach ($assessmentIds as $assessmentId) {
                        $avgScore = AssessmentResult::getAverageScore($assessmentId, $studentId);
                        if ($avgScore !== null) {
                            $studentAssessmentAverages->push($avgScore);
                            $completedUniqueAssessments++;
                        }
                    }

                    if ($studentAssessmentAverages->count() > 0) {
                        $studentAverages->push($studentAssessmentAverages->avg());
                    }
                }

                $averageScore = $studentAverages->count() > 0 ? $studentAverages->avg() : 0;
                $completionRate = $totalUniqueAssessments > 0 ? ($completedUniqueAssessments / $totalUniqueAssessments) * 100 : 0;

                return [
                    'class' => $class->name,
                    'averageScore' => round($averageScore, 1),
                    'totalAssessments' => $totalUniqueAssessments,
                    'completionRate' => round($completionRate, 1),
                ];
            });

            return $comparison;
        } catch (Exception $e) {
            Log::error('Error fetching class performance comparison: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recent activities for activity feed
     */
    private function getRecentActivities()
    {
        try {
            $activities = [];

            // Recent assessment submissions
            $recentResults = AssessmentResult::with(['assessment', 'student'])
                ->latest()
                ->take(5)
                ->get();

            foreach ($recentResults as $result) {
                $activities[] = [
                    'type' => 'assessment_submission',
                    'title' => 'Assessment Submitted',
                    'description' => "{$result->student->name} completed {$result->assessment->title}",
                    'score' => $result->score,
                    'timestamp' => $result->created_at,
                    'icon' => 'FileText',
                ];
            }

            // Recent topic completions
            $recentCompletions = CompletedTopic::with(['user', 'topic'])
                ->latest()
                ->take(5)
                ->get();

            foreach ($recentCompletions as $completion) {
                $activities[] = [
                    'type' => 'topic_completion',
                    'title' => 'Topic Completed',
                    'description' => "{$completion->user->name} completed {$completion->topic->name}",
                    'timestamp' => $completion->created_at,
                    'icon' => 'CheckCircle',
                ];
            }

            // Sort by timestamp and take latest 10
            $activities = collect($activities)
                ->sortByDesc('timestamp')
                ->take(10)
                ->values();

            return $activities;
        } catch (Exception $e) {
            Log::error('Error fetching recent activities: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get alerts and notifications
     */
    private function getAlerts()
    {
        try {
            $alerts = [];

            // Low performance alerts
            $lowPerformingStudents = AssessmentResult::select('student_id')
                ->where('score', '<', 50)
                ->where('created_at', '>=', Carbon::now()->subDays(7))
                ->distinct()
                ->count();

            if ($lowPerformingStudents > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'Low Performance Alert',
                    'message' => "{$lowPerformingStudents} students scored below 50% this week",
                    'action' => 'View Details',
                    'priority' => 'high',
                ];
            }

            // Incomplete assessments
            $incompleteAssessments = AssessmentResult::where('status', 'started')
                ->where('created_at', '<=', Carbon::now()->subHours(24))
                ->count();

            if ($incompleteAssessments > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'title' => 'Incomplete Assessments',
                    'message' => "{$incompleteAssessments} assessments started but not completed",
                    'action' => 'Send Reminders',
                    'priority' => 'medium',
                ];
            }

            // Inactive students
            $inactiveStudents = User::where('role', 'student')
                ->where('last_login_at', '<=', Carbon::now()->subDays(7))
                ->count();

            if ($inactiveStudents > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'Inactive Students',
                    'message' => "{$inactiveStudents} students haven't logged in for a week",
                    'action' => 'Contact Students',
                    'priority' => 'medium',
                ];
            }

            return $alerts;
        } catch (Exception $e) {
            Log::error('Error fetching alerts: ' . $e->getMessage());
            return [];
        }
    }
}
