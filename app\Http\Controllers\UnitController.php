<?php

namespace App\Http\Controllers;

use App\Models\Subject;
use App\Models\Unit;
use App\Models\Lesson;
use App\Models\SchoolClass;
use App\Models\Topic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class UnitController extends Controller
{
    /**
     * Display a listing of the units for a subject.
     */
    public function __construct()
    {
        abort_if(Auth::user()->role !== 'admin', 403);
    }
    public function index(Request $request, $class_id)
    {

        $class = SchoolClass::findOrFail($class_id);
        $subject = Subject::where('class_id', $class->id)->first();

        if (!$subject) {
            return redirect()->route('subjects.index', ['class_id' => $class_id])->with('error', 'No subject found for this class. Please create a subject first.');
        }

        if ($request->has('subject_id') && $request->subject_id !== null) {
            $subject = Subject::findOrFail($request->subject_id);
        }
        $subjects = Subject::where('class_id', $class->id)->get();

        $units = Unit::where('subject_id', $subject->id)->latest()->paginate(10);


        return Inertia::render('units/Index', [
            'units' => $units,
            'subjects' => $subjects,
            'subject' => $subject,
            'class' => $class,
        ]);
    }


    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'subject_id' => 'required|exists:subjects,id',
        ]);

        $unit = Unit::create([
            'name' => $validated['name'],
            'subject_id' => $validated['subject_id'],
        ]);

        return redirect()->back()->with('success', 'Unit created successfully');
    }

    /**
     * Update the specified unit in storage.
     */
    public function update(Request $request, $id)
    {
        $unit = Unit::findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $unit->update([
            'name' => $validated['name'],
        ]);

        return redirect()->back()->with('success', 'Unit updated successfully');
    }

    /**
     * Remove the specified unit from storage.
     */
    public function destroy($id)
    {
        $unit = Unit::findOrFail($id);
        $unit->delete();

        return redirect()->back()->with('success', 'Unit deleted successfully');
    }

    /**
     * Reorder units.
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'units' => 'required|array',
            'units.*.id' => 'required|exists:units,id',
            'units.*.order' => 'required|integer|min:0',
        ]);

        foreach ($request->units as $unitData) {
            Unit::where('id', $unitData['id'])->update(['order' => $unitData['order']]);
        }

        return response()->json(['message' => 'Units reordered successfully']);
    }

    /**
     * Convert a topic to a unit-level lesson.
     */
    public function convertTopicToLesson(Request $request, $topicId)
    {
        $topic = Topic::with(['lessons', 'unit'])->findOrFail($topicId);
        $unit = $topic->unit;

        // Create a new lesson at the unit level
        $unitLesson = Lesson::create([
            'title' => $topic->name,
            'description' => $topic->description,
            'unit_id' => $unit->id,
            'topic_id' => null,
            'order' => (Lesson::where('unit_id', $unit->id)->max('order') ?? 0) + 1,
            'is_published' => true,
        ]);

        // Move all content from the topic's first lesson (if any) to the new unit-level lesson
        if ($topic->lessons->isNotEmpty()) {
            $firstLesson = $topic->lessons->first();

            foreach ($firstLesson->contentBlocks as $contentBlock) {
                $contentBlock->lesson_id = $unitLesson->id;
                $contentBlock->save();
            }
        }

        // Delete the topic and its lessons
        foreach ($topic->lessons as $lesson) {
            if ($lesson->id !== $firstLesson->id) {
                $lesson->delete();
            }
        }

        $topic->delete();

        return redirect()->route('units.dashboard', $unit->id)
            ->with('success', 'Topic converted to lesson successfully');
    }
}
